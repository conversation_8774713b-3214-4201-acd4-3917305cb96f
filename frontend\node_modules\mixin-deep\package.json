{"name": "mixin-deep", "description": "Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone.", "version": "1.3.2", "homepage": "https://github.com/jonschlinkert/mixin-deep", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/mixin-deep", "bugs": {"url": "https://github.com/jonschlinkert/mixin-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3", "should": "^13.1.3"}, "keywords": ["deep", "extend", "key", "keys", "merge", "mixin", "object", "prop", "properties", "util", "values"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["defaults-deep", "extend-shallow", "merge-deep", "mixin-object"]}, "lint": {"reflinks": true}}}